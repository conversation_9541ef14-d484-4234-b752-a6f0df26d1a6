{"name": "chatbot-rag-extension", "displayName": "<PERSON><PERSON>", "description": "AI-powered coding assistant with RAG capabilities", "version": "0.1.0", "engines": {"vscode": "^1.80.0"}, "categories": ["Programming Languages", "Machine Learning", "Other"], "activationEvents": ["onStartupFinished", "onView:ragChatView"], "main": "./out/extension.js", "files": ["out", "src/assets"], "contributes": {"commands": [{"command": "chatbot-rag.startChat", "title": "RAG Assistant: <PERSON>", "category": "RAG Assistant"}, {"command": "chatbot-rag.askQuestion", "title": "RAG Assistant: Ask Question", "category": "RAG Assistant"}, {"command": "chatbot-rag.openSettings", "title": "RAG Assistant: <PERSON> Settings", "category": "RAG Assistant"}, {"command": "chatbot-rag.showDiff", "title": "RAG Assistant: Show Diff", "category": "RAG Assistant"}, {"command": "chatbot-rag.login", "title": "RAG Assistant: <PERSON><PERSON>", "category": "RAG Assistant"}, {"command": "chatbot-rag.logout", "title": "RAG Assistant: <PERSON><PERSON><PERSON>", "category": "RAG Assistant"}, {"command": "chatbot-rag.startServer", "title": "RAG Assistant: Start Server", "category": "RAG Assistant"}, {"command": "chatbot-rag.stopServer", "title": "RAG Assistant: Stop Server", "category": "RAG Assistant"}, {"command": "chatbot-rag.checkServerStatus", "title": "RAG Assistant: Check Server Status", "category": "RAG Assistant"}, {"command": "chatbot-rag.indexWorkspace", "title": "RAG Assistant: Index Workspace", "category": "RAG Assistant"}, {"command": "chatbot-rag.renameChatHistory", "title": "RAG Assistant: <PERSON><PERSON>", "category": "RAG Assistant"}, {"command": "chatbot-rag.newChatSession", "title": "RAG Assistant: <PERSON> Chat Session", "category": "RAG Assistant"}, {"command": "chatbot-rag.deleteChatSession", "title": "RAG Assistant: <PERSON><PERSON><PERSON>", "category": "RAG Assistant"}, {"command": "chatbot-rag.analyzeCode", "title": "RAG Assistant: <PERSON><PERSON><PERSON> Code", "category": "RAG Assistant"}, {"command": "chatbot-rag.refactorCode", "title": "RAG Assistant: Refactor Code", "category": "RAG Assistant"}, {"command": "chatbot-rag.generateDocs", "title": "RAG Assistant: Generate Documentation", "category": "RAG Assistant"}, {"command": "chatbot-rag.generateTests", "title": "RAG Assistant: Generate Tests", "category": "RAG Assistant"}, {"command": "chatbot-rag.understandCodebase", "title": "RAG Assistant: Understand Codebase", "category": "RAG Assistant"}, {"command": "chatbot-rag.askAgent", "title": "RAG Assistant: Ask Agent", "category": "RAG Assistant"}, {"command": "chatbot-rag.invalidateCache", "title": "RAG Assistant: <PERSON><PERSON><PERSON><PERSON>", "category": "RAG Assistant"}], "viewsContainers": {"activitybar": [{"id": "rag-assistant", "title": "RAG Assistant", "icon": "resources/dark/dependency.svg"}]}, "menus": {"editor/context": [{"when": "editorHasSelection", "command": "chatbot-rag.analyzeCode", "group": "ragAssistant@1"}, {"when": "editorHasSelection", "command": "chatbot-rag.refactorCode", "group": "ragAssistant@2"}, {"when": "editorHasSelection", "command": "chatbot-rag.generateDocs", "group": "ragAssistant@3"}, {"when": "editorHasSelection", "command": "chatbot-rag.generateTests", "group": "ragAssistant@4"}, {"command": "chatbot-rag.understandCodebase", "group": "ragAssistant@5"}, {"command": "chatbot-rag.askAgent", "group": "ragAssistant@6"}, {"command": "chatbot-rag.invalidateCache", "group": "ragAssistant@7"}]}, "views": {"rag-assistant": [{"id": "ragChatView", "name": "Cha<PERSON>", "type": "webview"}]}, "configuration": {"title": "RAG Assistant", "properties": {"ragAssistant.apiUrl": {"type": "string", "default": "http://localhost:8000", "description": "URL of the RAG API service"}, "ragAssistant.markdownSupport": {"type": "boolean", "default": true, "description": "Enable markdown rendering in chat messages"}, "ragAssistant.syntaxHighlighting": {"type": "boolean", "default": true, "description": "Enable syntax highlighting for code blocks"}, "ragAssistant.maxHistoryItems": {"type": "number", "default": 50, "description": "Maximum number of history items to store"}, "ragAssistant.authEnabled": {"type": "boolean", "default": false, "description": "Enable authentication for the RAG API service"}, "ragAssistant.authType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>", "bearer", "basic"], "default": "<PERSON><PERSON><PERSON><PERSON>", "description": "Type of authentication to use"}, "ragAssistant.apiKey": {"type": "string", "default": "", "description": "API key for authentication (when authType is 'apiKey')"}, "ragAssistant.bearerToken": {"type": "string", "default": "", "description": "Bearer token for authentication (when authType is 'bearer')"}, "ragAssistant.username": {"type": "string", "default": "", "description": "Username for basic authentication (when authType is 'basic')"}, "ragAssistant.password": {"type": "string", "default": "", "description": "Password for basic authentication (when authType is 'basic')"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/diff": "^7.0.2", "@types/node": "^16.0.0", "@types/uuid": "^10.0.0", "@types/vscode": "^1.80.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "typescript": "^4.9.0"}, "dependencies": {"@types/event-source-polyfill": "^1.0.5", "axios": "^1.6.0", "diff": "^5.1.0", "event-source-polyfill": "^1.0.31", "highlight.js": "^11.7.0", "marked": "^4.3.0", "uuid": "^9.0.0"}}