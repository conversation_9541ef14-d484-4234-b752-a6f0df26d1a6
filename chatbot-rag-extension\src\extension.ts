import * as vscode from 'vscode';
import { RagChatController } from './ragChatController';
import { RagChatViewProvider } from './ragChatViewProvider';
import { SettingsManager } from './settingsManager';
import { DiffViewProvider } from './diffViewProvider';
import { AuthenticationManager } from './authenticationManager';
import { ServerManager } from './serverManager';
import { CodeAnalyzer } from './codeAnalyzer';
import { CodeIndexer } from './codeIndexer';
import { AgentController } from './agentController';
import { CacheInvalidator } from './cacheInvalidator';

// Create output channel for debugging
let outputChannel: vscode.OutputChannel;

export function activate(context: vscode.ExtensionContext) {
    // Initialize output channel
    outputChannel = vscode.window.createOutputChannel('RAG Assistant');
    outputChannel.show();
    outputChannel.appendLine('RAG Assistant extension is activating...');

    try {
        // Create instances of our controller and view provider
        outputChannel.appendLine('Creating chat controller and view provider...');
        const chatController = new RagChatController(context);
        const chatViewProvider = new RagChatViewProvider(context.extensionUri);

        // Create agent controller for ADK integration
        outputChannel.appendLine('Creating agent controller for ADK integration...');
        const agentController = new AgentController(context);

        // Register the webview provider
        outputChannel.appendLine('Registering webview provider...');

        // Set up the chatViewProvider to handle messages
        chatViewProvider.setMessageHandler(async (message) => {
            try {
                outputChannel.appendLine(`Received message: ${JSON.stringify(message)}`);
                console.log('Extension received message:', message);

                switch (message.type) {
                case 'askQuestion':
                    if (message.question) {
                        await chatController.handleQuestion(message.question, chatViewProvider);
                    }
                    break;

                case 'clearChat':
                    await chatController.clearChatHistory();
                    break;

                case 'loadHistoryItem':
                    if (typeof message.index === 'number') {
                        await chatController.loadHistoryItem(message.index, chatViewProvider);
                    }
                    break;

                case 'applyEdit':
                    if (message.messageId && message.fileName) {
                        await chatController.applyEditsToFile(
                            message.messageId,
                            message.fileName,
                            chatViewProvider
                        );
                    }
                    break;

                case 'viewFile':
                case 'openFile':
                    if (message.fileName || message.filePath) {
                        await chatController.openFileForViewing(message.fileName || message.filePath);
                    }
                    break;

                case 'findCodeToken':
                    if (message.token) {
                        const result = await chatController.findCodeToken(
                            message.token,
                            message.filePath,
                            message.tokenType
                        );

                        // Send the result back to the webview
                        if (message.tokenId) {
                            chatViewProvider.postMessage({
                                type: 'tokenSearchResult',
                                tokenId: message.tokenId,
                                found: !!result
                            });
                        }
                    }
                    break;

                case 'getTokenInfo':
                    if (message.token) {
                        const tokenInfo = await chatController.getTokenInfo(message.token);

                        // Send the token info back to the webview
                        if (message.tokenId) {
                            chatViewProvider.postMessage({
                                type: 'tokenInfo',
                                tokenId: message.tokenId,
                                info: tokenInfo || { name: message.token }
                            });
                        }
                    }
                    break;

                case 'getTokenHistory':
                    const history = await chatController.getTokenHistory();
                    chatViewProvider.postMessage({
                        type: 'tokenHistory',
                        history
                    });
                    break;

                case 'renameChatHistory':
                    if (typeof message.index === 'number') {
                        if (message.newTitle) {
                            // Directly rename the session with the provided title
                            await chatController.renameChatHistoryItem(
                                message.index,
                                message.newTitle,
                                chatViewProvider
                            );
                        } else {
                            // Execute the rename command (legacy behavior)
                            vscode.commands.executeCommand('chatbot-rag.renameChatHistory');
                        }
                    }
                    break;

                case 'newChatSession':
                    // Create a new chat session
                    await chatController.createNewSession(chatViewProvider);
                    break;

                case 'deleteChatSession':
                    if (typeof message.index === 'number') {
                        // Delete the chat session
                        try {
                            await chatController.deleteSession(message.index, chatViewProvider);
                            // Send confirmation message
                            chatViewProvider.postMessage({
                                type: 'sessionDeleted',
                                success: true,
                                index: message.index
                            });
                        } catch (error) {
                            console.error('Error deleting session:', error);
                            // Send error message
                            chatViewProvider.postMessage({
                                type: 'sessionDeleted',
                                success: false,
                                error: error instanceof Error ? error.message : String(error)
                            });
                        }
                    }
                    break;

                case 'applyChanges':
                    if (message.blockId && message.filePath && message.code) {
                        await chatController.applyChangesToFile(
                            message.blockId,
                            message.filePath,
                            message.code,
                            chatViewProvider
                        );
                    }
                    break;

                case 'undoChanges':
                    if (message.blockId && message.filePath) {
                        await chatController.undoChangesToFile(
                            message.blockId,
                            message.filePath,
                            chatViewProvider
                        );
                    }
                    break;
                }
            } catch (error) {
                outputChannel.appendLine(`Error handling message: ${error}`);
                console.error('Error handling message:', error);

                // Send error back to webview
                chatViewProvider.postMessage({
                    type: 'error',
                    message: `Error processing your request: ${error instanceof Error ? error.message : 'Unknown error'}`
                });
            }
        });

        // Register the webview provider only once
        const viewProvider = vscode.window.registerWebviewViewProvider(
            'ragChatView',
            chatViewProvider
        );

        // Register commands
        outputChannel.appendLine('Registering commands...');
        const startChatCommand = vscode.commands.registerCommand('chatbot-rag.startChat', () => {
            outputChannel.appendLine('Executing startChat command...');
            vscode.commands.executeCommand('workbench.view.extension.rag-assistant');
        });

        const askQuestionCommand = vscode.commands.registerCommand('chatbot-rag.askQuestion', async () => {
            outputChannel.appendLine('Executing askQuestion command...');
            const question = await vscode.window.showInputBox({
                placeHolder: 'Ask a question...',
                prompt: 'Enter your question'
            });

            if (question) {
                await chatController.handleQuestion(question, chatViewProvider);
            }
        });

        const openSettingsCommand = vscode.commands.registerCommand('chatbot-rag.openSettings', () => {
            outputChannel.appendLine('Executing openSettings command...');
            SettingsManager.openSettings();
        });

        const showDiffCommand = vscode.commands.registerCommand('chatbot-rag.showDiff', async () => {
            outputChannel.appendLine('Executing showDiff command...');

            // Get the active text editor
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active text editor');
                return;
            }

            // Create a sample diff for demonstration
            const fileName = editor.document.fileName;
            const originalContent = editor.document.getText();
            const sampleEdit = {
                start: { line: 0, character: 0 },
                end: { line: 0, character: 0 },
                text: '// This is a sample diff\n'
            };

            // Show the diff
            const fileDiff = DiffViewProvider.generateFileDiff(
                fileName,
                originalContent,
                [sampleEdit]
            );

            const shouldApply = await DiffViewProvider.showDiff(fileDiff);

            if (shouldApply) {
                // Apply the edit
                await editor.edit(editBuilder => {
                    const range = new vscode.Range(
                        new vscode.Position(sampleEdit.start.line, sampleEdit.start.character),
                        new vscode.Position(sampleEdit.end.line, sampleEdit.end.character)
                    );
                    editBuilder.replace(range, sampleEdit.text);
                });

                vscode.window.showInformationMessage('Changes applied');
            } else {
                vscode.window.showInformationMessage('Changes discarded');
            }
        });

        const loginCommand = vscode.commands.registerCommand('chatbot-rag.login', async () => {
            outputChannel.appendLine('Executing login command...');

            const authManager = AuthenticationManager.getInstance();

            // Enable authentication if it's not already enabled
            if (!authManager.authConfig.enabled) {
                await SettingsManager.updateSetting('authEnabled', true);
                vscode.window.showInformationMessage('Authentication has been enabled');
            }

            // Authenticate
            const authenticated = await authManager.authenticate();

            if (authenticated) {
                vscode.window.showInformationMessage('Successfully authenticated');
            } else {
                vscode.window.showErrorMessage('Authentication failed');
            }
        });

        const logoutCommand = vscode.commands.registerCommand('chatbot-rag.logout', async () => {
            outputChannel.appendLine('Executing logout command...');

            const authManager = AuthenticationManager.getInstance();

            // Logout
            await authManager.logout();

            vscode.window.showInformationMessage('Successfully logged out');
        });

        // Server management commands
        const serverManager = ServerManager.getInstance();

        const startServerCommand = vscode.commands.registerCommand('chatbot-rag.startServer', async () => {
            outputChannel.appendLine('Executing startServer command...');
            await serverManager.startServer();
        });

        const stopServerCommand = vscode.commands.registerCommand('chatbot-rag.stopServer', async () => {
            outputChannel.appendLine('Executing stopServer command...');
            await serverManager.stopServer();
        });

        const checkServerStatusCommand = vscode.commands.registerCommand('chatbot-rag.checkServerStatus', async () => {
            outputChannel.appendLine('Executing checkServerStatus command...');
            const isRunning = await serverManager.checkServerStatus();
            if (isRunning) {
                vscode.window.showInformationMessage('RAG server is running');
            } else {
                vscode.window.showWarningMessage('RAG server is not running. Use the "Start Server" command to start it.');
            }
        });

        // Register command to rename chat history items
        const renameChatHistoryCommand = vscode.commands.registerCommand('chatbot-rag.renameChatHistory', async () => {
            outputChannel.appendLine('Executing renameChatHistory command...');

            // Get the chat history
            const history = await chatController.getChatHistory();

            if (history.length === 0) {
                vscode.window.showInformationMessage('No chat sessions to rename');
                return;
            }

            // Create QuickPick items from history
            const items = history.map((item: any, index: number) => ({
                label: item.title || item.question.substring(0, 30) + '...',
                description: new Date(item.timestamp).toLocaleString(),
                index: index
            }));

            // Show QuickPick to select a history item
            const selectedItem = await vscode.window.showQuickPick(items, {
                placeHolder: 'Select a chat session to rename'
            });

            if (!selectedItem) {
                return; // User cancelled
            }

            // Show input box to enter new title
            const newTitle = await vscode.window.showInputBox({
                prompt: 'Enter a new title for this chat session',
                value: selectedItem.label
            });

            if (!newTitle) {
                return; // User cancelled
            }

            // Rename the history item
            const success = await chatController.renameChatHistoryItem(
                selectedItem.index,
                newTitle,
                chatViewProvider
            );

            if (success) {
                vscode.window.showInformationMessage('Chat session renamed successfully');
            } else {
                vscode.window.showErrorMessage('Failed to rename chat session');
            }
        });

        // Register command to create a new chat session
        const newChatSessionCommand = vscode.commands.registerCommand('chatbot-rag.newChatSession', async () => {
            outputChannel.appendLine('Executing newChatSession command...');
            await chatController.createNewSession(chatViewProvider);
        });

        // Register command to delete a chat session
        const deleteChatSessionCommand = vscode.commands.registerCommand('chatbot-rag.deleteChatSession', async () => {
            outputChannel.appendLine('Executing deleteChatSession command...');

            // Get the chat history
            const history = await chatController.getChatHistory();

            if (history.length === 0) {
                vscode.window.showInformationMessage('No chat sessions to delete');
                return;
            }

            // Create QuickPick items from history
            const items = history.map((item: any, index: number) => ({
                label: item.title || item.question.substring(0, 30) + '...',
                description: new Date(item.timestamp).toLocaleString(),
                index: index
            }));

            // Show QuickPick to select a history item
            const selectedItem = await vscode.window.showQuickPick(items, {
                placeHolder: 'Select a chat session to delete'
            });

            if (!selectedItem) {
                return; // User cancelled
            }

            // Confirm deletion
            const confirmed = await vscode.window.showWarningMessage(
                `Are you sure you want to delete the chat session "${selectedItem.label}"?`,
                'Delete',
                'Cancel'
            );

            if (confirmed !== 'Delete') {
                return; // User cancelled
            }

            // Delete the session
            await chatController.deleteSession(selectedItem.index, chatViewProvider);
        });

        // Register agent commands
        outputChannel.appendLine('Registering agent commands...');

        // Command to analyze code with the agent
        const analyzeCodeCommand = vscode.commands.registerCommand('chatbot-rag.analyzeCode', async () => {
            outputChannel.appendLine('Executing analyzeCode command...');

            // Get the active text editor
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor');
                return;
            }

            // Get the code to analyze
            const code = editor.document.getText(editor.selection.isEmpty ? undefined : editor.selection);
            const language = editor.document.languageId;

            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Analyzing code...',
                cancellable: false
            }, async () => {
                try {
                    // Call the agent to analyze the code
                    const result = await agentController.analyzeCode(code, language);

                    // Add the result to the chat view
                    chatViewProvider.addMessage(
                        `## Code Analysis Results\n\n${result.answer}`,
                        false
                    );

                    // Show the chat view
                    vscode.commands.executeCommand('workbench.view.extension.rag-assistant');

                    return result;
                } catch (error) {
                    vscode.window.showErrorMessage(`Error analyzing code: ${error}`);
                    return null;
                }
            });
        });

        // Command to refactor code with the agent
        const refactorCodeCommand = vscode.commands.registerCommand('chatbot-rag.refactorCode', async () => {
            outputChannel.appendLine('Executing refactorCode command...');

            // Get the active text editor
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor');
                return;
            }

            // Get the code to refactor
            const code = editor.document.getText(editor.selection.isEmpty ? undefined : editor.selection);
            const language = editor.document.languageId;

            // Ask for the refactoring goal
            const goal = await vscode.window.showInputBox({
                prompt: 'What is your refactoring goal?',
                placeHolder: 'e.g., readability, performance, error handling'
            });

            if (!goal) return; // User cancelled

            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Refactoring code...',
                cancellable: false
            }, async () => {
                try {
                    // Call the agent to refactor the code
                    const result = await agentController.refactorCode(code, language, goal);

                    // Add the result to the chat view
                    chatViewProvider.addMessage(
                        `## Refactoring Results (Goal: ${goal})\n\n${result.answer}`,
                        false
                    );

                    // Show the chat view
                    vscode.commands.executeCommand('workbench.view.extension.rag-assistant');

                    return result;
                } catch (error) {
                    vscode.window.showErrorMessage(`Error refactoring code: ${error}`);
                    return null;
                }
            });
        });

        // Command to generate documentation with the agent
        const generateDocsCommand = vscode.commands.registerCommand('chatbot-rag.generateDocs', async () => {
            outputChannel.appendLine('Executing generateDocs command...');

            // Get the active text editor
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor');
                return;
            }

            // Get the code to document
            const code = editor.document.getText(editor.selection.isEmpty ? undefined : editor.selection);
            const language = editor.document.languageId;

            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Generating documentation...',
                cancellable: false
            }, async () => {
                try {
                    // Call the agent to generate documentation
                    const result = await agentController.generateDocumentation(code, language);

                    // Add the result to the chat view
                    chatViewProvider.addMessage(
                        `## Documentation Results\n\n${result.answer}`,
                        false
                    );

                    // Show the chat view
                    vscode.commands.executeCommand('workbench.view.extension.rag-assistant');

                    return result;
                } catch (error) {
                    vscode.window.showErrorMessage(`Error generating documentation: ${error}`);
                    return null;
                }
            });
        });

        // Command to generate tests with the agent
        const generateTestsCommand = vscode.commands.registerCommand('chatbot-rag.generateTests', async () => {
            outputChannel.appendLine('Executing generateTests command...');

            // Get the active text editor
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor');
                return;
            }

            // Get the code to test
            const code = editor.document.getText(editor.selection.isEmpty ? undefined : editor.selection);
            const language = editor.document.languageId;

            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Generating tests...',
                cancellable: false
            }, async () => {
                try {
                    // Call the agent to generate tests
                    const result = await agentController.generateTests(code, language);

                    // Add the result to the chat view
                    chatViewProvider.addMessage(
                        `## Test Results\n\n${result.answer}`,
                        false
                    );

                    // Show the chat view
                    vscode.commands.executeCommand('workbench.view.extension.rag-assistant');

                    return result;
                } catch (error) {
                    vscode.window.showErrorMessage(`Error generating tests: ${error}`);
                    return null;
                }
            });
        });

        // Command to ask the agent a question
        const askAgentCommand = vscode.commands.registerCommand('chatbot-rag.askAgent', async () => {
            outputChannel.appendLine('Executing askAgent command...');

            // Get the active text editor for context
            const editor = vscode.window.activeTextEditor;

            // Prepare context if an editor is active
            const context = editor ? {
                fileName: editor.document.fileName,
                language: editor.document.languageId,
                selectedText: editor.selection.isEmpty ? undefined : editor.document.getText(editor.selection),
                fullText: editor.document.getText()
            } : undefined;

            // Ask for the question
            const query = await vscode.window.showInputBox({
                prompt: 'What would you like to ask the agent?',
                placeHolder: 'Enter your question...'
            });

            if (!query) return; // User cancelled

            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Processing with agent...',
                cancellable: false
            }, async () => {
                try {
                    // Add the user's question to the chat view
                    chatViewProvider.addMessage(query, true);

                    // Call the agent
                    const result = await agentController.runMainAgent(query, context);

                    // Add the result to the chat view
                    chatViewProvider.addMessage(result.answer, false);

                    // Show the chat view
                    vscode.commands.executeCommand('workbench.view.extension.rag-assistant');

                    return result;
                } catch (error) {
                    vscode.window.showErrorMessage(`Error processing with agent: ${error}`);

                    // Add the error to the chat view
                    chatViewProvider.addMessage(
                        `Error processing your request: ${error instanceof Error ? error.message : String(error)}`,
                        false
                    );

                    return null;
                }
            });
        });

        // Command to understand the codebase structure
        const understandCodebaseCommand = vscode.commands.registerCommand('chatbot-rag.understandCodebase', async () => {
            outputChannel.appendLine('Executing understandCodebase command...');

            // Ask for the question about the codebase
            const query = await vscode.window.showInputBox({
                prompt: 'What would you like to understand about the codebase?',
                placeHolder: 'e.g., What is the architecture? How are components organized?'
            });

            if (!query) return; // User cancelled

            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Analyzing codebase structure...',
                cancellable: false
            }, async () => {
                try {
                    // Add the user's question to the chat view
                    chatViewProvider.addMessage(`**Codebase Question:** ${query}`, true);

                    // Call the codebase understanding agent
                    const result = await agentController.understandCodebase(query);

                    // Add the result to the chat view
                    chatViewProvider.addMessage(
                        `## Codebase Understanding\n\n${result.answer}`,
                        false
                    );

                    // Show the chat view
                    vscode.commands.executeCommand('workbench.view.extension.rag-assistant');

                    return result;
                } catch (error) {
                    vscode.window.showErrorMessage(`Error analyzing codebase: ${error}`);

                    // Add the error to the chat view
                    chatViewProvider.addMessage(
                        `Error analyzing codebase: ${error instanceof Error ? error.message : String(error)}`,
                        false
                    );

                    return null;
                }
            });
        });

        // Command to invalidate the cache
        const invalidateCacheCommand = vscode.commands.registerCommand('chatbot-rag.invalidateCache', async () => {
            outputChannel.appendLine('Executing invalidateCache command...');

            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Invalidating cache...',
                cancellable: false
            }, async () => {
                try {
                    // Invalidate all caches
                    await cacheInvalidator.invalidateAllCaches();

                    // Show success message
                    vscode.window.showInformationMessage('Cache invalidated successfully');

                    return true;
                } catch (error) {
                    vscode.window.showErrorMessage(`Error invalidating cache: ${error instanceof Error ? error.message : String(error)}`);
                    return false;
                }
            });
        });

        // Add subscriptions to context
        context.subscriptions.push(
            outputChannel,
            viewProvider,
            startChatCommand,
            askQuestionCommand,
            openSettingsCommand,
            showDiffCommand,
            loginCommand,
            logoutCommand,
            startServerCommand,
            stopServerCommand,
            checkServerStatusCommand,
            renameChatHistoryCommand,
            newChatSessionCommand,
            deleteChatSessionCommand,
            // Agent commands
            analyzeCodeCommand,
            refactorCodeCommand,
            generateDocsCommand,
            generateTestsCommand,
            askAgentCommand,
            understandCodebaseCommand,
            // Cache commands
            invalidateCacheCommand
        );

        // Initialize code analyzer and indexer
        outputChannel.appendLine('Initializing code analyzer and indexer...');
        const codeAnalyzer = CodeAnalyzer.getInstance(context);
        // Initialize code indexer (for future use)
        CodeIndexer.getInstance(context);

        // Initialize cache invalidator
        outputChannel.appendLine('Initializing cache invalidator...');
        const cacheInvalidator = CacheInvalidator.getInstance(context);

        // Register indexing command
        const indexWorkspaceCommand = vscode.commands.registerCommand('chatbot-rag.indexWorkspace', async () => {
            outputChannel.appendLine('Executing indexWorkspace command...');
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Indexing workspace for code validation',
                cancellable: true
            }, async (_progress, _token) => {
                try {
                    await codeAnalyzer.indexWorkspace(true);
                    const status = codeAnalyzer.getIndexingStatus();
                    vscode.window.showInformationMessage(
                        `Workspace indexed successfully. Indexed ${status.fileCount} files with ${status.entityCount} code entities.`
                    );
                } catch (error) {
                    vscode.window.showErrorMessage(`Error indexing workspace: ${error}`);
                }
            });
        });

        // Add indexing command to subscriptions
        context.subscriptions.push(indexWorkspaceCommand);

        // Start indexing the workspace in the background
        codeAnalyzer.indexWorkspace().then(() => {
            outputChannel.appendLine('Workspace indexing completed');
        }).catch(error => {
            outputChannel.appendLine(`Error indexing workspace: ${error}`);
        });

        // Check server status on startup
        serverManager.checkServerStatus().then(isRunning => {
            if (isRunning) {
                outputChannel.appendLine('RAG server is already running');
            } else {
                outputChannel.appendLine('RAG server is not running');
                vscode.window.showInformationMessage(
                    'RAG server is not running. Would you like to start it now?',
                    'Yes', 'No'
                ).then(answer => {
                    if (answer === 'Yes') {
                        serverManager.startServer();
                    }
                });
            }
        });

        outputChannel.appendLine('RAG Assistant extension activated successfully!');
    } catch (error) {
        outputChannel.appendLine(`Error during activation: ${error}`);
        throw error;
    }
}

export function deactivate() {
    outputChannel.appendLine('RAG Assistant extension is deactivating...');
}

